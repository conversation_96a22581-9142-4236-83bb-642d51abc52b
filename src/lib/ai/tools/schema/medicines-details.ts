import { z } from "zod";

export const medicinesDetailsSchema = z.object({
  careCalendarId: z
    .string()
    .describe(
      "care calendar id is required get this from getComplaintDetailsById tool"
    )
    .min(1, "care_calendar_id is required"),
  medicines: z
    .array(
      z.object({
        id: z
          .union([z.string().min(1, "medicine id is required"), z.number()])
          .describe("medicine id"),
        name: z.string().optional().describe("medicine name is required"),
        dosage: z.number().describe("dosage of the medicine"),
        unit: z.string().describe("unit of the medicine dosage"),
        administrationRouteId: z
          .union([
            z.string().min(1, "route of the medicine is required"),
            z.number(),
          ])
          .describe("route of the medicine"),
        frequency: z
          .enum(["SID", "BID", "TID"])
          .describe("frequency of the medicine"),
      })
    )
    .min(1, "At least one medicine is required")
    .describe(
      "Array of medicine objects after based on checkMedicine tool, checkMedicineRoute tool"
    ),

  follow_up_required: z
    .boolean()
    .describe("Indicates if a follow-up is required"),
  follow_up_date: z
    .string()
    .nullable()
    .optional()
    .describe("Date of the follow up in ISO format required time also"),
  follow_up_date_gmt: z
    .string()
    .nullable()
    .optional()
    .describe("Convert the follow up date to GMT format"),
  vet_cosultant_type: z
    .union([z.literal(1000750001), z.literal(1000750002)])
    .describe("Type of vet consultant during prescription"),
});
