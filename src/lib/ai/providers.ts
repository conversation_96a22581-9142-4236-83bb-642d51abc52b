import {
  customProvider,
  extractReasoningMiddleware,
  wrapLanguageModel,
} from "ai";
import { groq } from "@ai-sdk/groq";
import { isTestEnvironment } from "../constants";
import {
  artifactModel,
  chatModel,
  reasoningModel,
  titleModel,
} from "./models.test";

export const myProvider = isTestEnvironment
  ? customProvider({
      languageModels: {
        "chat-model": chatModel,
        "chat-model-reasoning": reasoningModel,
        "title-model": titleModel,
        "artifact-model": artifactModel,
        "summarize-model": chatModel,
      },
    })
  : customProvider({
      languageModels: {
        "chat-model": groq("meta-llama/llama-4-scout-17b-16e-instruct"),
        "chat-model-reasoning": wrapLanguageModel({
          model: groq("mixtral-8x7b-32768"),
          middleware: extractReasoningMiddleware({ tagName: "think" }),
        }),
        "title-model": groq("meta-llama/llama-4-maverick-17b-128e-instruct"),
        "artifact-model": groq("meta-llama/llama-4-maverick-17b-128e-instruct"),
        "summarize-model": groq(
          "meta-llama/llama-4-maverick-17b-128e-instruct"
        ),
      },
    });
