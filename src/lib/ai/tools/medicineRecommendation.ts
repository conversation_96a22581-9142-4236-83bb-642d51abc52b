import { tool } from "ai";
import { z } from "zod";
import { getRecommendedMedicines } from "@/lib/services/medicines";
import { RequestOptions } from "@/types/RequestOption";

// Types and Interfaces
interface APIData {
    diagnosis: string[];
    breed: string;
    num_calvings: number;
    age: number;
    months_pregnant: number;
    months_since_calving: number;
    avg_lpd: number;
}

interface SearchData {
    query: string;
}

interface MedicineResponse {
    option1: any; // Replace with specific type based on your API response
    option2: any; // Replace with specific type based on your API response
}

interface ToolResponse {
    found: boolean;
    message: string;
    matches: MedicineResponse | any[];
}

// Configuration
const API_ENDPOINTS = {
    MEDICINE_PREDICTION: "https://ahs2.krushal.in/api/prediction/prescription/amul",
    RAG_SEARCH: "https://rag-pdf-v1mj.onrender.com/api/search"
} as const;

const API_CONFIG = {
    TIMEOUT: 30000, // 30 seconds
    MAX_RETRIES: 2,
    TOP_K_RESULTS: 10
} as const;

// Utility function to clean and parse JSON with NaN values
const safeJsonParse = (text: string): any => {
    try {
        // Replace NaN values with null before parsing
        const cleanedText = text
            .replace(/:\s*NaN/g, ': null')
            .replace(/,\s*NaN/g, ', null')
            .replace(/\[\s*NaN/g, '[null')
            .replace(/NaN\s*\]/g, 'null]')
            .replace(/NaN\s*,/g, 'null,')
            .replace(/,\s*NaN\s*}/g, ', null}')
            .replace(/{\s*NaN/g, '{null');
            
        return JSON.parse(cleanedText);
    } catch (error) {
        console.error("JSON parsing failed even after cleaning:", error);
        console.log("Problematic text (first 1000 chars):", text.substring(0, 1000));
        throw new Error(`Invalid JSON format: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
};

// Utility function for API calls with timeout
const fetchWithTimeout = async (url: string, options: RequestInit, timeout: number = API_CONFIG.TIMEOUT): Promise<Response> => {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    
    try {
        const response = await fetch(url, {
            ...options,
            signal: controller.signal
        });
        clearTimeout(timeoutId);
        return response;
    } catch (error) {
        clearTimeout(timeoutId);
        throw error;
    }
};

// Main function to fetch recommended medicines
const fetchRecommendedMedicines = async (payload: string[], query: string): Promise<MedicineResponse | string> => {
    try {
        // Input validation
        if (!payload || payload.length === 0) {
            return "No Diagnosis provided";
        }

        if (!query || query.trim().length === 0) {
            return "No case description provided";
        }

        // Prepare API payloads
        const medicineData: APIData = {
            diagnosis: payload,
            breed: "",
            num_calvings: 0,
            age: 0,
            months_pregnant: 0,
            months_since_calving: 0,
            avg_lpd: 0
        };

        const searchData: SearchData = {
            query: query.trim()
        };

        console.log("Making API calls with data:", { medicineData, searchData });

        // Make concurrent API calls
        const [response1] = await Promise.all([
            fetchWithTimeout(API_ENDPOINTS.MEDICINE_PREDICTION, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(medicineData)
            }),
            // fetchWithTimeout(API_ENDPOINTS.RAG_SEARCH, {
            //     method: "POST",
            //     headers: {
            //         "Content-Type": "application/json",
            //     },
            //     body: JSON.stringify(searchData)
            // })
        ]);

        console.log("API Response Status:", {
            medicine: response1.status,
            // search: response2.status
        });

        // Check response status
        if (!response1.ok) {
            const error1 = !response1.ok ? `Medicine API: ${response1.status} ${response1.statusText}` : null;
            // const error2 = !response2.ok ? `Search API: ${response2.status} ${response2.statusText}` : null;
            const errors = [error1].filter(Boolean).join(", ");
            
            console.error("API Error:", errors);
            return `Failed to fetch recommended medicines: ${errors}`;
        }

        // Parse responses with better error handling
        let responseData1, responseData2;
        
        try {
            const [text1] = await Promise.all([
                response1.text(),
                // response2.text()
            ]);
            
            console.log("Raw response lengths:", { 
                response1: text1.length, 
                // response2: text2.length 
            });
            
            // Use safe JSON parsing
            responseData1 = safeJsonParse(text1);
            // responseData2 = safeJsonParse(text2);
            
        } catch (parseError) {
            console.error("JSON Parse Error:", parseError);
            return `Failed to parse API response: ${parseError instanceof Error ? parseError.message : 'Invalid JSON format'}`;
        }

        console.log("Response from APIs: ", {
            option1: responseData1,
            option2: responseData1
        });

        // Validate response data
        if (!responseData1) {
            return "No data received from APIs";
        }

        return {
            option1: responseData1,
            option2: responseData1
        };

    } catch (error) {
        console.error("Error in fetchRecommendedMedicines:", error);
        
        if (error instanceof Error) {
            if (error.name === 'AbortError') {
                return "Request timeout - API calls took too long";
            }
            return `Failed to fetch recommended medicines: ${error.message}`;
        }
        
        return "Failed to fetch recommended medicines: Unknown error";
    }
};

// Main tool export
export const medicineRecommendation = (options: RequestOptions) => tool({
    description: "Recommends medicines based on diagnosis and case description for veterinary cases",
    parameters: z.object({
        diagnosis: z.array(z.string())
            .min(1, "At least one diagnosis is required")
            .max(5, "Maximum 5 diagnoses allowed")
            .describe("Array of diagnoses for the case"),
        caseDescription: z.string()
            .min(1, "Case description is required")
            .describe("Detailed case description from context"),
        animalId: z.string().optional().describe("Optional animal ID for context"),
    }),
    execute: async ({ diagnosis, caseDescription, animalId }): Promise<ToolResponse> => {
        console.log("medicineRecommendation called with:", {
            diagnosisCount: diagnosis.length,
            caseDescriptionLength: caseDescription.length,
            animalId
        });

        // Input validation
        if (!diagnosis || diagnosis.length <= 0) {
            return {
                found: false,
                message: "At least 1 diagnosis is needed to check with the list",
                matches: [],
            };
        }

        // Filter out empty diagnoses
        const validDiagnoses = diagnosis.filter(d => d && d.trim().length > 0);
        
        if (validDiagnoses.length === 0) {
            return {
                found: false,
                message: "No valid diagnoses provided",
                matches: [],
            };
        }

        console.log("Fetching recommendations for:", validDiagnoses);
        
        try {
            const recommendedMedicinesResult = await getRecommendedMedicines(diagnosis,options, animalId);
            
            console.log("Recommendation result:", recommendedMedicinesResult);

            // Handle string responses (errors)
            if (typeof recommendedMedicinesResult === 'string') {
                return {
                    found: false,
                    message: recommendedMedicinesResult,
                    matches: [],
                };
            }

            // Handle successful responses
            if (!recommendedMedicinesResult) {
                return {
                    found: false,
                    message: "No matching medicines found",
                    matches: [],
                };
            }

            console.log("Recommendations successfully received");

            return {
                found: true,
                message: "Matching medicines found successfully",
                matches: recommendedMedicinesResult,
            };

        } catch (error) {
            console.error("Error in medicine recommendation execution:", error);
            
            return {
                found: false,
                message: "An unexpected error occurred while fetching recommendations",
                matches: [],
            };
        }
    }
});