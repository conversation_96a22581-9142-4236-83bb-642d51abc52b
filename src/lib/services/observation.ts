import axios from 'axios';
import { RequestOptions } from "@/types/RequestOption";
import { caseDescriptionSchema } from "../ai/tools/schema/case-description";
import z from 'zod';

type caseDescription = z.infer<typeof caseDescriptionSchema>;
export const updateCaseDescription = async (
  params: caseDescription,
  options: RequestOptions
) => {
  try {
    const payload = {
      care_calendar_id: params.careCalenderId,
      observation_notes_1: params.case_description,
    };

    fetch(`${options.baseUrl}/v2/observation-notes`, {
      method: "POST",
      headers: options.headers,
      body: JSON.stringify(payload),
    })
      .then(async (res) => {
        if (!res.ok) {
          const text = await res.text();
          throw new Error(`Status ${res.status}: ${text}`);
        }
        return res.json();
      })
      .then((data) => console.log("Success:", data))
      .catch((err) => console.error("Error:", err.message));

    return 'success';
  } catch (error) {
    console.log("update Case Description error", error);
    return { error };
  }
};

export const updateObservation = async (
  params: {
    careCalendarId: string;
    weight: number | null;
    temperature: number | null;
  },
  options: RequestOptions
) => {
  try {
    const payload = {
      config: [
        {
          type: 'numeric',
          options: [],
          validation: {
            required: {
              value: true,
              message: 'Required'
            }
          },
          reference_id: 1030000004
        },
        {
          type: 'numeric',
          options: [],
          validation: {
            required: {
              value: true,
              message: 'Required'
            }
          },
          reference_id: 1030000029
        }
      ],
      observation_category_id: 1001270101,
      care_calendar_id: params.careCalendarId,
      observations: [
        {
          care_calendar_classification_id:undefined,
          question_id: 1030000004,
          answers: [
            params.temperature
          ],
        },
        {
          care_calendar_classification_id:undefined,
          question_id: 1030000029,
          answers: [
            params.weight
          ],
        }
      ]
    }

    const existing_obs = await getObservation(
      {
        careCalendarId: params.careCalendarId,
        observation_category_id: 1001270101
      },
      options
    );
    console.log("existing_obs", existing_obs);
    const existing_obs_data = existing_obs?.data ?? [];

    if(existing_obs_data.length > 0){
        for(const obs of existing_obs_data){
          if(obs.reference_id === 1030000004){
            payload.observations[0].care_calendar_classification_id = obs.care_calendar_classification_id
          }
          if(obs.question_id === 1030000029){
              payload.observations[1].care_calendar_classification_id = obs.care_calendar_classification_id
          }
        }
    }
 

    fetch(`${options.baseUrl}/v2/observations`, {
      method: 'POST',
      headers: options.headers,
      body: JSON.stringify(payload),
    })
      .then(async (res) => {
        if (!res.ok) {
          const text = await res.text();
          throw new Error(`Status ${res.status}: ${text}`);
        }
        return res.json();
      })
      .then((data) => console.log("Success:", data))
      .catch((err) => console.error("Error:", err.message));

    return 'success';
  } catch (error) {
    console.log("update Observation error", error);
    return { error };
  }
};

export const getObservation = async (
  params: {
    careCalendarId: string;
    observation_category_id: number;
  },
  options: RequestOptions
) => {
  try {
    const response = await fetch(
      `${options.baseUrl}/v2/observations/${params.observation_category_id}?care_calendar_id=${params.careCalendarId}`,
      {
        method: 'GET',
        headers: options.headers,
      }
    );

    if (!response.ok) {
      const text = await response.text();
      throw new Error(`Status ${response.status}: ${text}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.log("get Observation error", error);
    return { error };
  }
};
