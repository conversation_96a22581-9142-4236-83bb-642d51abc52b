import { tool } from "ai";
import { z } from "zod";
import Fuse from "fuse.js";

import { RequestOptions } from "@/types/RequestOption";

export const followUp = (options: RequestOptions) =>
  tool({
    description: "tool to get follow up date if follow up is necessary",
    parameters: z.object({
      follow_up_required: z
        .boolean()
        .describe("Is a follow up required for this Ticket? "),
      follow_up_date: z
        .string()
        .describe("Date of the follow up in ISO format required time also"),
      follow_up_date_gmt: z
        .string()
        .describe("Convert the follow up date to GMT format"),
    }),
    execute: async ({
      follow_up_required,
      follow_up_date,
      follow_up_date_gmt,
    }) => {
      if (!follow_up_required) {
        return {
          found: false,
          message: "Follow up is not required",
          matches: [],
        };
      }
      if (!follow_up_date) {
        return {
          found: false,
          message: "Follow up date is required",
          matches: [],
        };
      }
      return {
        found: true,
        message: "Follow up date is recorded",
        follow_up_required: follow_up_required,
        follow_up_date: follow_up_date,
        follow_up_date_gmt: follow_up_date_gmt,
      };
    },
  });
