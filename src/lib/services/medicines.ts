import axios from "axios";
import { number, z } from "zod";
import { medicinesDetailsSchema } from "../ai/tools/schema/medicines-details";
import { RequestOptions } from "@/types/RequestOption";


export const medicineList = async (options: RequestOptions): Promise<any> => {
  try {
    const response = await axios.get(`${options.baseUrl}/medicine`, {
      headers: options.headers,
    });
    const medicineList = response.data;
    const medicineListArray = Object.keys(medicineList).map((key) => {
      const medicine = medicineList[key];
      return {
        id: medicine.medicine_id,
        name: medicine.medicine_name,
      };
    });
    return medicineListArray;
  } catch (error) {
    console.log("task list error", error);
    return { error };
  }
};

export type updateMedicinesParams = {
  activity_id: string;
  medicine: [
    {
      id: string;
      dosage: number;
      prescribed_info: {
        unit: string;
        route: number;
        frequency: string;
        status: string;
      };
    }
  ];
};

type medications = z.infer<typeof medicinesDetailsSchema> & {
  medication?: string;
  advicory_notes?: string;
};

export const updateMedicines = async (
  params: medications,
  options: RequestOptions
) => {
  try {
    // const getMedicinesByActivityId = await getMedicinesById(
    //   params.careCalendarId,
    //   options
    // );
    // console.log("getMedicinesById", getMedicinesById);
    // const previousMedicines =
    //   getMedicinesByActivityId.length > 0 ? getMedicinesByActivityId : [];

    const payload = {
      activity_id: params.careCalendarId,
      medicine: [
        ...params.medicines.map((medicine: medications["medicines"][0]) => ({
          id: medicine.id,
          dosage: medicine.dosage ?? 0,
          prescribed_info: {
            unit: medicine?.unit ?? "",
            route: medicine?.administrationRouteId ?? 0,
            frequency: medicine?.frequency ?? "",
            status: "PUBLISHED",
          },
        })),
      ] as [updateMedicinesParams["medicine"][0]],
      follow_up_required: params.follow_up_required,
      vet_cosultant_type: params.vet_cosultant_type,
      follow_up_date: params.follow_up_date,
      follow_up_date_gmt: params.follow_up_date_gmt,
      suggestions_advisory_notes: params.advicory_notes,
    };

    console.log("medicine params", JSON.stringify(payload));

    fetch(`${options.baseUrl}/v2/tasks/prescription/publish`, {
      method: "POST",
      headers: options.headers,
      body: JSON.stringify(payload),
    })
      .then(async (res) => {
        if (!res.ok) {
          const text = await res.text();
          throw new Error(`Status ${res.status}: ${text}`);
        }
        return res.json();
      })
      .then((data) => console.log("Success:", data))
      .catch((err) => console.error("Error:", err.message));

    return "success";
  } catch (error) {
    console.log("updateMedicines error", error);
    return { error };
  }
};

export const getMedicinesById = async (
  activityId: string,
  options: RequestOptions
) => {
  try {
    const url = `${options.baseUrl}/activity/prescription/${activityId}`;

    const response = await axios.get(url, { headers: options.headers });

    return response.data?.data?.selected?.medicine;
  } catch (error) {
    console.log("getMedicinesById error", error);
    return { error };
  }
};

export const loadReferences = async (
  options: RequestOptions,
  category_id: number
): Promise<any> => {
  try {
    const response = await axios.post(
      `${options.baseUrl}/oc/load-references-for-reference-categories`,
      {
        reference_category_array: [category_id],
      },
      {
        headers: options.headers,
      }
    );
    const routes = response.data?.reference_category_map[category_id] ?? [];
    const routesArray = routes.map((key: any) => {
      return {
        id: key.reference_id,
        name: key.reference_name_l10n?.ul || key.reference_name_l10n.en,
      };
    });
    return routesArray;
  } catch (error) {
    console.log("loadRoutes error", error);
    return { error };
  }
};

export const getRecommendedMedicines = async (
  payload: string[],
  options: RequestOptions,
  animalId?: string
): Promise<any> => {
  try {
    // Input validation
    if (!payload || payload.length === 0) {
      return "No Diagnosis provided";
    }

    // Prepare API payloads
    const medicineData = {
      diagnosis: payload,
      animal_id: animalId || "", // Optional animal ID for context
    };


    console.log("Making API calls with data:", { medicineData });

    // Make concurrent API calls
    const response1 = await fetch(`${options.baseUrl}/prediction/prescription/amul`, {
      method: "POST",
      headers: options.headers,
      body: JSON.stringify(medicineData),
    });

    if (!response1.ok) {
      throw new Error(`Error fetching recommended medicines: ${response1.statusText}`);
    }

    const data = await response1.json();
    return data;
  } catch (error) {
    console.error("Error in getRecommendedMedicines:", error);
    return { error };
  }
};
