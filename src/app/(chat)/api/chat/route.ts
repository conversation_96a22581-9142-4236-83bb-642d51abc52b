import {
  createDataStream,
  smoothStream,
  streamText,
  NoSuchToolError,
  generateObject,
} from "ai";
import { systemPrompt } from "@/lib/ai/prompts";
import { generateUUID } from "@/lib/utils";
import { isProductionEnvironment } from "@/lib/constants";
import { myProvider } from "@/lib/ai/providers";
import { postRequestBodySchema, type PostRequestBody } from "./schema";
import { generatePrescription } from "@/lib/ai/tools/generatePrescription";
import { getComplaintDetailsById } from "@/lib/ai/tools/getComplaintDetailsById";
import { checkDiagnosis } from "@/lib/ai/tools/checkDiagnosis";
import {
  checkMedicine,
  checkMedicineRoute,
} from "@/lib/ai/tools/checkMedicine";
import { RequestOptions } from "@/types/RequestOption";
import { recordCaseDescription } from "@/lib/ai/tools/recordCaseDescription";
import { getVetConsultantType } from "@/lib/ai/tools/vetConsultantType";
import { followUp } from "@/lib/ai/tools/followUp";
import { medicineRecommendation } from "@/lib/ai/tools/medicineRecommendation";

export const maxDuration = 60;

export async function POST(request: Request) {
  let requestBody: PostRequestBody;
  let requestHeaders: Headers;
  let authToken: string;
  let baseURL: string;

  try {
    requestHeaders = request.headers;
    authToken =
      requestHeaders.get("authtoken")?.trim() || process.env.Token || "";
    baseURL =
      requestHeaders.get("baseurl")?.trim() ||
      process.env.BASE_URL?.trim() ||
      "";
  } catch (_) {
    return new Response("Invalid request headers", { status: 400 });
  }

  const options: RequestOptions = {
    baseUrl: baseURL,
    headers: {
      "Content-Type": "application/json",
      Token: authToken,
    },
  };

  try {
    requestBody = await request.json();
    // requestBody = postRequestBodySchema.parse(json);
  } catch (_) {
    return new Response("Invalid request body", { status: 400 });
  }

  try {
    const { id, message, selectedChatModel, messages } = requestBody;

    // Log token usage information
    console.log("=== Token Usage Log ===");
    console.log("Chat ID:", id);
    console.log("Selected Model:", selectedChatModel);
    console.log("Message Count:", messages.length);
    console.log("Input Tokens (estimated):", JSON.stringify(messages).length / 4);
    console.log(JSON.stringify(messages)) // Rough estimation
    console.log("Timestamp:", new Date().toISOString());
    console.log("=======================");

    // Create a simple stream without database operations
    const stream = createDataStream({
      execute: (dataStream) => {
        const result = streamText({
          model: myProvider.languageModel("chat-model"),
          system: systemPrompt({ selectedChatModel }),
          messages,
          maxSteps: 10,
          maxRetries: 3,
          experimental_activeTools:
            selectedChatModel === "chat-model-reasoning"
              ? []
              : [
                  "getComplaintDetailsById",
                  "checkDiagnosis",
                  "medicineRecommendation",
                  "checkMedicine",
                  "generatePrescription",
                  "recordCaseDescription",
                  "checkMedicineRoute",
                  "getVetConsultantType",
                  "followUp",
                ],
          experimental_transform: smoothStream({ chunking: "word" }),
          experimental_generateMessageId: generateUUID,
          tools: {
            getComplaintDetailsById: getComplaintDetailsById(options),
            recordCaseDescription: recordCaseDescription(options),
            checkDiagnosis: checkDiagnosis(options),
            medicineRecommendation : medicineRecommendation(options),
            checkMedicine: checkMedicine(options),
            checkMedicineRoute: checkMedicineRoute(options),
            getVetConsultantType: getVetConsultantType(options),
            followUp: followUp(options),
            generatePrescription: generatePrescription(options),
            
          },

          onError: ({ error }) => {
            console.error("Error from the streamText: ", error);
          },
        });

        result.consumeStream();

        result.mergeIntoDataStream(dataStream, {
          sendReasoning: true,
        });
      },
      onError: (error) => {
        console.log(error);
        return "Oops, an error occurred!";
      },
    });

    return new Response(stream);
  } catch (error) {
    console.error("Error in chat API:", error);
    return new Response("An error occurred while processing your request!", {
      status: 500,
    });
  }
}

export async function GET(request: Request) {
  // Return an empty response for GET requests
  // In a real app, this would retrieve chat history
  return new Response(null, { status: 204 });
}

export async function DELETE(request: Request) {
  // Return a success response for DELETE requests
  // In a real app, this would delete a chat
  return new Response(null, { status: 200 });
}
