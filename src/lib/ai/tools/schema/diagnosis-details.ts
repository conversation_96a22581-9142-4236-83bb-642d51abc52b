import { z } from "zod";

export const diagnosisDetailsSchema = z.object({
  careCalendarId: z
    .string()
    .describe(
      "care calendar id is required get this from getComplaintDetailsById tool"
    )
    .min(1, "care_calendar_id is required"),
  diagnosis: z
    .array(
      z.object({
        id: z
          .union([z.string().min(1, "diagnosis id is required"), z.number()])
          .describe("diagnosis id"),
        name: z
          .string()
          .min(1, "diagnosis name is required")
          .describe("diagnosis name"),
      })
    )
    .min(1, "diagnosis is required")
    .describe("diagnosis is required"),
});
